# Generated by Django 5.0.3 on 2025-04-04 12:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0010_remove_registrationlink_role_remove_membership_role_and_more'),
        # Removed assistants dependency since the app doesn't exist
    ]

    operations = [
        # Commented out since assistants app doesn't exist
        # migrations.AddField(
        #     model_name='registrationlink',
        #     name='accessible_folders',
        #     field=models.ManyToManyField(blank=True, help_text='Select folders the user joining via this link should have access to.', related_name='registration_links', to='assistants.assistantfolder'),
        # ),
    ]
