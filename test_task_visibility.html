<!DOCTYPE html>
<html>
<head>
    <title>Test Task Visibility Logic</title>
</head>
<body>
    <h1>Task Visibility Logic Test</h1>
    <div id="results"></div>

    <script>
        // Function to determine if a task should be visible based on player progress
        function shouldTaskBeVisible(taskId, gameState) {
            // Get the current role and role challenges completed
            const currentRole = gameState.currentRole;
            const roleChallengesCompleted = gameState.roleChallengesCompleted || 0;
            
            // Define task order for each role
            const roleTaskOrder = {
                'applicant': ['cover_letter'],
                'junior_assistant': ['email_response', 'meeting_notes', 'project_summary'],
                'sales_associate': ['sales_pitch', 'client_follow_up', 'market_analysis'],
                'marketing_coordinator': ['campaign_proposal', 'social_media_strategy', 'event_planning'],
                'project_manager': ['project_plan', 'risk_assessment', 'stakeholder_communication'],
                'senior_analyst': ['data_analysis', 'strategic_recommendation', 'presentation_prep'],
                'team_lead': ['team_motivation', 'performance_review', 'conflict_resolution'],
                'department_manager': ['budget_planning', 'process_improvement', 'cross_department_collaboration'],
                'senior_manager': ['strategic_planning', 'leadership_development', 'organizational_change'],
                'director': ['vision_setting', 'board_presentation', 'company_transformation']
            };
            
            // Get the task order for the current role
            const currentRoleTasks = roleTaskOrder[currentRole] || [];
            
            // Find the index of the task in the current role's task list
            const taskIndex = currentRoleTasks.indexOf(taskId);
            
            // If the task is not in the current role, check if it's from a completed role
            if (taskIndex === -1) {
                // Check if this task belongs to a completed role
                const completedRoles = gameState.completedRoles || [];
                for (const completedRole of completedRoles) {
                    const completedRoleTasks = roleTaskOrder[completedRole] || [];
                    if (completedRoleTasks.includes(taskId)) {
                        // Task is from a completed role, so it should be visible
                        return true;
                    }
                }
                // Task is not from current role or completed roles, don't show it
                return false;
            }
            
            // Task is in current role - show it only if player has reached this task
            // Player can see tasks up to their current progress + 1 (the current task they're working on)
            return taskIndex <= roleChallengesCompleted;
        }

        // Test scenarios
        const testScenarios = [
            {
                name: "Junior Assistant - Just started (0 tasks completed)",
                gameState: {
                    currentRole: 'junior_assistant',
                    roleChallengesCompleted: 0,
                    completedRoles: ['applicant']
                },
                tasks: ['cover_letter', 'email_response', 'meeting_notes', 'project_summary'],
                expected: [true, true, false, false] // Should see cover_letter (completed) and email_response (current)
            },
            {
                name: "Junior Assistant - Completed 1 task",
                gameState: {
                    currentRole: 'junior_assistant',
                    roleChallengesCompleted: 1,
                    completedRoles: ['applicant']
                },
                tasks: ['cover_letter', 'email_response', 'meeting_notes', 'project_summary'],
                expected: [true, true, true, false] // Should see first 3 tasks
            },
            {
                name: "Junior Assistant - Completed 2 tasks",
                gameState: {
                    currentRole: 'junior_assistant',
                    roleChallengesCompleted: 2,
                    completedRoles: ['applicant']
                },
                tasks: ['cover_letter', 'email_response', 'meeting_notes', 'project_summary'],
                expected: [true, true, true, true] // Should see all tasks
            }
        ];

        // Run tests
        let results = '<h2>Test Results:</h2>';
        
        testScenarios.forEach((scenario, index) => {
            results += `<h3>Scenario ${index + 1}: ${scenario.name}</h3>`;
            results += `<p>Current Role: ${scenario.gameState.currentRole}, Challenges Completed: ${scenario.gameState.roleChallengesCompleted}</p>`;
            results += '<ul>';
            
            let allPassed = true;
            scenario.tasks.forEach((taskId, taskIndex) => {
                const result = shouldTaskBeVisible(taskId, scenario.gameState);
                const expected = scenario.expected[taskIndex];
                const passed = result === expected;
                allPassed = allPassed && passed;
                
                results += `<li style="color: ${passed ? 'green' : 'red'}">
                    Task: ${taskId} - Expected: ${expected}, Got: ${result} ${passed ? '✓' : '✗'}
                </li>`;
            });
            
            results += '</ul>';
            results += `<p style="color: ${allPassed ? 'green' : 'red'}; font-weight: bold;">
                Overall: ${allPassed ? 'PASSED' : 'FAILED'}
            </p><hr>`;
        });

        document.getElementById('results').innerHTML = results;
    </script>
</body>
</html>
