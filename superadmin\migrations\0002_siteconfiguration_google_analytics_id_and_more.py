# Generated by Django 5.2 on 2025-05-15 05:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('superadmin', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='siteconfiguration',
            name='google_analytics_id',
            field=models.CharField(blank=True, help_text='Google Analytics tracking ID (e.g., UA-XXXXXXXX-X or G-XXXXXXXXXX).', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='siteconfiguration',
            name='google_verification_tag',
            field=models.CharField(blank=True, help_text='Google Search Console verification tag.', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='siteconfiguration',
            name='site_description',
            field=models.TextField(default='AI-powered game for corporate training and prompt engineering skills', help_text='The description of the site used in SEO meta tags.', max_length=300),
        ),
        migrations.AddField(
            model_name='siteconfiguration',
            name='site_keywords',
            field=models.CharField(default='corporate training, AI game, prompt engineering, corporate learning', help_text='Comma-separated keywords for SEO.', max_length=200),
        ),
        migrations.AddField(
            model_name='siteconfiguration',
            name='site_title',
            field=models.CharField(default='Corporate Prompt Master', help_text='The title of the site used in SEO meta tags.', max_length=100),
        ),
    ]
