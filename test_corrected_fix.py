#!/usr/bin/env python
"""
Test the corrected fix for OpenAI client initialization
"""

try:
    from openai import OpenAI
    import httpx
    
    print("🔧 Testing corrected fixed OpenAI client initialization...")
    
    # Test the corrected approach with proxy=None (not proxies=None)
    try:
        http_client = httpx.Client(
            timeout=httpx.Timeout(30.0),
            follow_redirects=True,
            verify=True,
            # Explicitly set proxy to None to avoid any global proxy settings
            proxy=None
        )
        client = OpenAI(
            api_key="test-key",
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            http_client=http_client
        )
        print("✅ Corrected OpenAI client initialization successful!")
        
        # Clean up
        http_client.close()
        
    except Exception as e:
        print(f"❌ Corrected OpenAI client initialization failed: {e}")
        import traceback
        traceback.print_exc()
    
except ImportError as e:
    print(f"❌ Import failed: {e}")

print("\nTest completed")
