# HTTP-only development settings
# Import all settings from the main settings file
from .settings import *

# Override HTTPS-related settings for HTTP development
SECURE_SSL_REDIRECT = False
CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False

# Update CORS to only allow HTTP origins
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

print("🔓 HTTP-only development mode enabled")
print("📍 Server will be available at: http://127.0.0.1:8000/")
print("⚠️  HTTPS security features are disabled for development")
