# Generated by Django 5.0.3 on 2025-04-03 19:35
# Data migration to transfer permissions from old Role model to django-guardian

from django.conf import settings # Import settings
from django.db import migrations
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType

# Mapping from old Role boolean fields to new permission codenames (app_label.codename)
# Note: Standard add/change/delete/view perms are assumed for models where applicable.
# We map the *custom* logic represented by the booleans.
ROLE_BOOLEAN_TO_PERM_MAP = {
    # Company Permissions (accounts.Company)
    'can_edit_company_settings': 'accounts.change_company_settings',
    'can_manage_billing': 'accounts.manage_billing',
    'can_delete_company': 'accounts.delete_company_object', # Custom perm name
    'can_manage_directory_listing': 'accounts.manage_directory_listing',
    'can_manage_members': 'accounts.manage_members',
    'can_manage_invites_links': 'accounts.manage_invites_links',
    # Activity Log (accounts.ActivityLog - assuming view is sufficient)
    # 'view_company_activity': 'accounts.view_activitylog', # Standard view perm

    # Membership Permissions (accounts.Membership) - Less common, map specific actions
    'can_manage_folder_access': 'accounts.manage_folder_access', # Custom perm on Membership
    # 'can_change_role': 'accounts.change_membership_role', # Custom perm on Membership

    # AssistantFolder Permissions (assistants.AssistantFolder)
    'can_manage_folders': [ # Maps to standard CRUD on AssistantFolder
        'assistants.add_assistantfolder',
        'assistants.change_assistantfolder',
        'assistants.delete_assistantfolder',
        'assistants.view_assistantfolder',
    ],

    # Assistant Permissions (assistants.Assistant)
    'can_create_assistants': 'assistants.add_assistant', # Standard add perm
    'can_edit_assistants': 'assistants.change_assistant', # Standard change perm
    'can_delete_assistants': 'assistants.delete_assistant', # Standard delete perm
    'can_view_assistant_usage': 'assistants.view_assistant_usage', # Custom perm
    'can_view_assistant_analytics': 'assistants.view_assistant_analytics', # Custom perm
    'can_access_all_private_assistants': 'assistants.access_all_private', # Custom perm
    'can_create_assistant_tokens': 'assistants.create_assistant_token', # Custom perm
}

# Define permission sets for each role based on the old boolean flags
# These are permissions granted *on the Company object* unless specified otherwise
ADMIN_PERMS_COMPANY = [
    'accounts.change_company_settings',
    'accounts.manage_billing',
    # 'accounts.delete_company_object', # Owner only
    'accounts.manage_directory_listing',
    'accounts.manage_members',
    'accounts.manage_invites_links',
    'accounts.view_company_activity', # Admins should see activity
    'accounts.manage_folder_access', # Can manage folder access *for* memberships
    'accounts.change_membership_role', # Can change roles *of* memberships
    'accounts.manage_company_assistants', # <<< ADDED: Allow managing assistants via wizard/views
    'assistants.add_assistantfolder',
    'assistants.change_assistantfolder',
    'assistants.delete_assistantfolder',
    'assistants.view_assistantfolder', # Implied by change/delete
    'assistants.add_assistant',
    'assistants.change_assistant', # Can edit any assistant in the company
    'assistants.delete_assistant', # Can delete any assistant in the company
    'assistants.view_assistant_usage', # Can view usage for any assistant
    'assistants.view_assistant_analytics', # Can view analytics for any assistant
    'assistants.access_all_private', # Can bypass folder checks
    'assistants.create_assistant_token', # Can create tokens for any assistant
]

MEMBER_PERMS_COMPANY = [
    # Limited company perms
    'accounts.view_company_activity', # Members might see some activity? Adjust as needed.
    # Folder perms (view only by default, specific access granted below)
    'assistants.view_assistantfolder',
    # Assistant perms (can create, edit/delete own - requires object-level checks later)
    'accounts.manage_company_assistants', # <<< ADDED: Allow managing assistants via wizard/views
    'assistants.add_assistant',
    'assistants.change_assistant', # Grant base perm, view logic enforces 'own'
    'assistants.delete_assistant', # Grant base perm, view logic enforces 'own'
    'assistants.view_assistant_usage', # Grant base perm, view logic enforces scope
    # No analytics, no access_all, no token creation by default
]

VIEWER_PERMS_COMPANY = [
    # Very limited company perms
    'accounts.view_company_activity', # Viewers might see some activity? Adjust as needed.
    # Folder perms (view only by default, specific access granted below)
    'assistants.view_assistantfolder',
    # Assistant perms (view usage/analytics only - requires object-level checks later)
    'assistants.view_assistant_usage', # Grant base perm, view logic enforces scope
    'assistants.view_assistant_analytics', # Grant base perm, view logic enforces scope
    # No create/edit/delete/access_all/token creation
]

# Permissions specifically for the Company Owner
OWNER_PERMS_COMPANY = ADMIN_PERMS_COMPANY + [
    'accounts.delete_company_object', # Owner can delete company
]

# Permissions granted *on AssistantFolder objects* based on accessible_folders M2M
FOLDER_ACCESS_PERMS = [
    'assistants.view_assistantfolder',
    'assistants.change_assistantfolder', # Assuming access means ability to modify folder contents (assistants)
    # Add specific assistant perms within the folder if needed, e.g.:
    'assistants.view_assistant',
    'assistants.change_assistant',
    'assistants.delete_assistant',
]


def forwards_migrate_roles(apps, schema_editor):
    """
    Migrates permissions from the old Role model and Membership.accessible_folders
    to django-guardian object-level permissions.
    """
    # Ensure guardian is installed and models are available
    try:
        from guardian.shortcuts import assign_perm, remove_perm
        from guardian.models import UserObjectPermission, GroupObjectPermission
    except ImportError:
        raise ImportError("django-guardian is required for this migration. Please install it.")

    User = apps.get_model(settings.AUTH_USER_MODEL)
    Company = apps.get_model('accounts', 'Company')
    Membership = apps.get_model('accounts', 'Membership')
    # Role = apps.get_model('accounts', 'Role') # Old Role model <<< COMMENTED OUT - Model likely deleted earlier

    # Try to get AssistantFolder model, but handle the case where it doesn't exist
    try:
        AssistantFolder = apps.get_model('assistants', 'AssistantFolder')
    except LookupError:
        print("Warning: assistants.AssistantFolder model not found. Skipping folder permissions.")
        AssistantFolder = None

    # --- Step 1: Assign Owner Permissions ---
    print("\nAssigning Owner permissions...")
    for company in Company.objects.all():
        owner = company.owner
        if owner:
            print(f"  Assigning Owner perms for Company '{company.name}' to User '{owner.username}'")
            for perm_codename in OWNER_PERMS_COMPANY:
                try:
                    assign_perm(perm_codename, owner, company)
                except Permission.DoesNotExist:
                    print(f"    Warning: Permission '{perm_codename}' not found. Skipping.")
                except Exception as e:
                    print(f"    Error assigning perm '{perm_codename}' to owner '{owner.username}' for company '{company.name}': {e}")
        else:
            print(f"  Warning: Company '{company.name}' (ID: {company.id}) has no owner.")

    # --- Step 2: Assign Permissions based on Old Roles ---
    # <<< COMMENTED OUT SECTION - Cannot access old Role model >>>
    # print("\nAssigning permissions based on old Roles...")
    # for membership in Membership.objects.select_related('user', 'company', 'role').prefetch_related('accessible_folders').all():
    #     user = membership.user
    #     company = membership.company
    #     # old_role = membership.role # Cannot access role if field/model removed
    #     accessible_folders = membership.accessible_folders.all()

    #     # if not user or not company or not old_role: # Cannot check old_role
    #     if not user or not company:
    #         print(f"  Skipping Membership ID {membership.id} due to missing user or company.")
    #         continue

    #     # print(f"  Processing Membership: User '{user.username}', Company '{company.name}', Role '{old_role.name}'") # Cannot print old_role.name

    #     # Determine permission set based on old role name - LOGIC REMOVED
    #     perms_to_assign_company = []
    #     # if old_role.name == 'Admin':
    #     #     perms_to_assign_company = ADMIN_PERMS_COMPANY
    #     # elif old_role.name == 'Member':
    #     #     perms_to_assign_company = MEMBER_PERMS_COMPANY
    #     # elif old_role.name == 'Viewer':
    #     #     perms_to_assign_company = VIEWER_PERMS_COMPANY
    #     # else:
    #     #     print(f"    Warning: Unknown role name '{old_role.name}' for Membership ID {membership.id}. Skipping role perm assignment.")

    #     # Assign company-level permissions - LOGIC REMOVED
    #     # for perm_codename in perms_to_assign_company:
    #     #      # Avoid granting owner-only delete perm unless user is the owner
    #     #     if perm_codename == 'accounts.delete_company_object' and user != company.owner:
    #     #         continue
    #     #     try:
    #     #         assign_perm(perm_codename, user, company)
    #     #         # print(f"    Assigned Company perm '{perm_codename}'")
    #     #     except Permission.DoesNotExist:
    #     #         print(f"    Warning: Permission '{perm_codename}' not found. Skipping.")
    #     #     except Exception as e:
    #     #         print(f"    Error assigning perm '{perm_codename}' to user '{user.username}' for company '{company.name}': {e}")

    #     # --- Step 3: Assign Folder Permissions based on M2M ---
    #     # This part might still be relevant if accessible_folders exists on Membership at this stage
    #     print(f"    Assigning folder permissions for User '{user.username}' for {len(accessible_folders)} folders...")
    #     for folder in accessible_folders:
    #         print(f"      Assigning perms for Folder '{folder.name}' (ID: {folder.id})")
    #         for perm_codename in FOLDER_ACCESS_PERMS:
    #             try:
    #                 assign_perm(perm_codename, user, folder)
    #                 # print(f"        Assigned Folder perm '{perm_codename}'")
    #             except Permission.DoesNotExist:
    #                 print(f"        Warning: Permission '{perm_codename}' not found. Skipping.")
    #             except Exception as e:
    #                 print(f"        Error assigning perm '{perm_codename}' to user '{user.username}' for folder '{folder.name}': {e}")

    print("\nSkipped assigning permissions based on old Roles due to missing Role model.")
    print("Folder permissions based on accessible_folders might still be assigned if field exists.")
    # <<< END COMMENTED OUT SECTION >>>

    print("\nPermission migration finished (potentially incomplete due to missing Role model).")


def backwards_remove_roles(apps, schema_editor):
    """Removes the permissions assigned by the forwards migration."""
    # This is complex to reverse perfectly without knowing what other perms might exist.
    # A simple approach is to remove all object permissions managed by guardian
    # for the relevant models, but this could remove permissions assigned elsewhere.
    # For safety, we'll make this irreversible or just print a warning.
    print("\nReversing role migration is complex and potentially destructive.")
    print("Manually remove permissions using guardian admin or shell if necessary.")
    # raise migrations.IrreversibleError("Cannot reliably reverse the role-to-guardian permission migration.")

    # --- Optional: More aggressive (but risky) reversal ---
    # try:
    #     from guardian.models import UserObjectPermission, GroupObjectPermission
    #     Company = apps.get_model('accounts', 'Company')
    #     AssistantFolder = apps.get_model('assistants', 'AssistantFolder')
    #     Membership = apps.get_model('accounts', 'Membership') # Needed?
    #     Assistant = apps.get_model('assistants', 'Assistant') # Needed?

    #     company_ct = ContentType.objects.get_for_model(Company)
    #     folder_ct = ContentType.objects.get_for_model(AssistantFolder)
    #     # Add other relevant ContentTypes if needed

    #     print("Removing UserObjectPermissions for Company and AssistantFolder...")
    #     UserObjectPermission.objects.filter(content_type__in=[company_ct, folder_ct]).delete()
    #     print("Removing GroupObjectPermissions for Company and AssistantFolder...")
    #     GroupObjectPermission.objects.filter(content_type__in=[company_ct, folder_ct]).delete()
    #     print("Permissions potentially removed. Review manually.")
    # except Exception as e:
    #     print(f"Error during reversal: {e}")
    #     raise migrations.IrreversibleError("Error occurred during permission removal.")


class Migration(migrations.Migration):

    dependencies = [
        # Ensure this runs after the models have the 'permissions' Meta option defined
        # and after guardian's own migrations.
        ('accounts', '0009_b_alter_company_options'), # <<< RENAMED dependency
        # Removed assistants dependency since the app doesn't exist
        ('guardian', '0002_generic_permissions_index'), # Corrected guardian dependency
    ]

    operations = [
        migrations.RunPython(forwards_migrate_roles, backwards_remove_roles),
    ]
