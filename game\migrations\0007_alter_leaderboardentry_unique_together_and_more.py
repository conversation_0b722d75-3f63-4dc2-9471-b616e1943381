# Generated by Django 5.2 on 2025-05-12 10:11

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0006_merge_20250512_1006'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='leaderboardentry',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='leaderboardentry',
            name='game_id',
            field=models.CharField(blank=True, help_text='Identifier for the specific game', max_length=100),
        ),
        migrations.AddField(
            model_name='leaderboardentry',
            name='game_name',
            field=models.Char<PERSON>ield(blank=True, help_text='Name of the game', max_length=255),
        ),
        migrations.AlterUniqueTogether(
            name='leaderboardentry',
            unique_together={('leaderboard', 'user', 'game_id')},
        ),
    ]
