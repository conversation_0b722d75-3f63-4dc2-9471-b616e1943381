#!/usr/bin/env python
import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession
from django.contrib.auth.models import User

print("=== Cleaning up ALL duplicate GameSessions ===")

# 1. Clean up duplicate sessions by session_id (for anonymous users)
print("\n1. Cleaning up duplicate anonymous sessions by session_id...")
session_ids = GameSession.objects.filter(user__isnull=True).values_list('session_id', flat=True).distinct()

for session_id in session_ids:
    if session_id:  # Skip None session_ids
        sessions = GameSession.objects.filter(session_id=session_id, user__isnull=True).order_by('-updated_at')
        if sessions.count() > 1:
            print(f"Session ID {session_id}: {sessions.count()} duplicates found")
            # Keep the most recent one
            latest = sessions.first()
            duplicates = sessions.exclude(id=latest.id)
            print(f"  Keeping session {latest.id}, deleting {duplicates.count()} duplicates")
            duplicates.delete()

# 2. Clean up duplicate sessions by user
print("\n2. Cleaning up duplicate user sessions...")
for user in User.objects.all():
    sessions = GameSession.objects.filter(user=user).order_by('-updated_at')
    if sessions.count() > 1:
        print(f"User {user.username}: {sessions.count()} sessions found")
        # Keep the most recent one
        latest = sessions.first()
        duplicates = sessions.exclude(id=latest.id)
        print(f"  Keeping session {latest.id}, deleting {duplicates.count()} duplicates")
        duplicates.delete()

# 3. Clean up orphaned sessions (no user and no session_id)
print("\n3. Cleaning up orphaned sessions...")
orphaned = GameSession.objects.filter(user__isnull=True, session_id__isnull=True)
if orphaned.exists():
    print(f"Found {orphaned.count()} orphaned sessions, deleting...")
    orphaned.delete()

# 4. Handle the special case of AnonymousUser (which shouldn't exist)
print("\n4. Cleaning up AnonymousUser sessions...")
try:
    anon_user = User.objects.get(username='AnonymousUser')
    anon_sessions = GameSession.objects.filter(user=anon_user)
    if anon_sessions.exists():
        print(f"Found {anon_sessions.count()} sessions for AnonymousUser, deleting...")
        anon_sessions.delete()
except User.DoesNotExist:
    print("No AnonymousUser found (good)")

print("\n=== Final count ===")
print(f"Total sessions remaining: {GameSession.objects.count()}")

# Show remaining sessions
print("\n=== Remaining sessions ===")
for s in GameSession.objects.all().order_by('id'):
    user_info = s.user.username if s.user else f"Anonymous({s.session_id})"
    print(f"ID: {s.id}, User: {user_info}, Updated: {s.updated_at}")

print("\n=== Cleanup complete ===")
