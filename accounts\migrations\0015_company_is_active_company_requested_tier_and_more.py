# Generated by Django 5.2 on 2025-04-07 18:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0014_registrationlink_qr_code'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='is_active',
            field=models.BooleanField(db_index=True, default=False, help_text='Whether the company is approved and active on the platform.'),
        ),
        migrations.AddField(
            model_name='company',
            name='requested_tier',
            field=models.CharField(blank=True, choices=[('Gold', 'Gold'), ('Silver', 'Silver'), ('Bronze', 'Bronze'), ('Standard', 'Standard')], help_text='Tier requested by the company owner, pending superadmin approval.', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='tier_change_pending',
            field=models.BooleanField(db_index=True, default=False, help_text='Indicates if a tier change request is pending approval.'),
        ),
    ]
