#!/usr/bin/env python
"""
HTTPS Development Server Runner

This script runs the Django development server with HTTPS support using django-extensions.
It automatically generates self-signed certificates if they don't exist.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Run the HTTPS development server."""
    
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
    
    # Check if we're in the right directory
    if not Path('manage.py').exists():
        print("Error: manage.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Create certs directory if it doesn't exist
    certs_dir = Path('certs')
    certs_dir.mkdir(exist_ok=True)
    
    cert_file = certs_dir / 'localhost.crt'
    key_file = certs_dir / 'localhost.key'
    
    # Generate self-signed certificate if it doesn't exist
    if not cert_file.exists() or not key_file.exists():
        print("Generating self-signed SSL certificate...")
        try:
            # Generate private key
            subprocess.run([
                'openssl', 'genrsa', '-out', str(key_file), '2048'
            ], check=True, capture_output=True)
            
            # Generate certificate
            subprocess.run([
                'openssl', 'req', '-new', '-x509', '-key', str(key_file),
                '-out', str(cert_file), '-days', '365', '-subj',
                '/C=US/ST=State/L=City/O=Organization/CN=localhost'
            ], check=True, capture_output=True)
            
            print(f"✅ SSL certificate generated: {cert_file}")
            print(f"✅ SSL private key generated: {key_file}")
            
        except subprocess.CalledProcessError as e:
            print("❌ Error generating SSL certificate with OpenSSL.")
            print("Falling back to django-extensions auto-generated certificate...")
            # Remove partial files
            cert_file.unlink(missing_ok=True)
            key_file.unlink(missing_ok=True)
        except FileNotFoundError:
            print("❌ OpenSSL not found. Using django-extensions auto-generated certificate...")
    
    # Run the HTTPS development server
    print("\n🚀 Starting HTTPS development server...")
    print("📍 Server will be available at: https://127.0.0.1:8000/")
    print("⚠️  You may see a security warning in your browser - this is normal for self-signed certificates.")
    print("   Click 'Advanced' and 'Proceed to 127.0.0.1 (unsafe)' to continue.\n")
    
    try:
        if cert_file.exists() and key_file.exists():
            # Use our generated certificate
            cmd = [
                sys.executable, 'manage.py', 'runserver_plus',
                '--cert-file', str(cert_file),
                '--key-file', str(key_file),
                '127.0.0.1:8000'
            ]
        else:
            # Let django-extensions generate its own certificate
            cmd = [
                sys.executable, 'manage.py', 'runserver_plus',
                '--cert-file', str(cert_file),
                '127.0.0.1:8000'
            ]
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped.")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\nTrying fallback method...")
        
        # Fallback: try without specifying certificate files
        try:
            subprocess.run([
                sys.executable, 'manage.py', 'runserver_plus',
                '--cert-file', 'localhost',
                '127.0.0.1:8000'
            ])
        except Exception as fallback_error:
            print(f"❌ Fallback also failed: {fallback_error}")
            print("\nPlease try running manually:")
            print("python manage.py runserver_plus --cert-file localhost 127.0.0.1:8000")

if __name__ == '__main__':
    main()
