# Generated by Django 5.0.3 on 2025-04-03 19:03

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0009_migrate_roles_to_guardian'),
    ]

    operations = [
        # migrations.RemoveField(
        #     model_name='registrationlink',
        #     name='role',
        # ),
        # migrations.RemoveField(
        #     model_name='membership',
        #     name='role',
        # ),
        # migrations.RemoveField(
        #     model_name='companyinvitation',
        #     name='role',
        # ),
        migrations.AlterModelOptions(
            name='membership',
            options={'ordering': ['company__name', 'user__username']},
        ),
        # Commented out since accessible_folders field doesn't exist anymore
        # migrations.RemoveField(
        #     model_name='membership',
        #     name='accessible_folders',
        # ),
        # migrations.DeleteModel(
        #     name='Role',
        # ),
    ]
