from django.db import models
from django.contrib.auth.models import User
import json

# Import the corporate Company model
from corporate.models import Company as CorporateCompany

class GameSession(models.Model):
    """
    Model to store game session data for each user.
    For anonymous users, we'll use session ID instead of user.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Company relationship - null=True allows anonymous sessions
    company = models.ForeignKey(CorporateCompany, on_delete=models.CASCADE, null=True, blank=True,
                               related_name='game_sessions', help_text="The company this game session belongs to")
    team = models.CharField(max_length=100, blank=True, help_text="Team or department within the company")

    # Game state fields
    current_role = models.CharField(max_length=50, default="applicant")
    performance_score = models.IntegerField(default=0)
    challenges_completed = models.IntegerField(default=0)
    role_challenges_completed = models.IntegerField(default=0)
    game_completed = models.BooleanField(default=False)
    current_manager = models.CharField(max_length=50, default="hr")
    current_task = models.CharField(max_length=50, default="cover_letter")
    completed_roles = models.TextField(default="[]")  # Stored as JSON
    first_task_pending = models.BooleanField(default=True)
    next_task_pending = models.BooleanField(default=False)  # Added for promotion handling

    def get_completed_roles(self):
        """Get completed roles as a list"""
        return json.loads(self.completed_roles)

    def set_completed_roles(self, roles_list):
        """Set completed roles from a list"""
        self.completed_roles = json.dumps(roles_list)

    def to_dict(self):
        """Convert model to dictionary for game state"""
        result = {
            "current_role": self.current_role,
            "performance_score": self.performance_score,
            "challenges_completed": self.challenges_completed,
            "role_challenges_completed": self.role_challenges_completed,
            "game_completed": self.game_completed,
            "current_manager": self.current_manager,
            "current_task": self.current_task,
            "completed_roles": self.get_completed_roles(),
            "first_task_pending": self.first_task_pending,
            "next_task_pending": self.next_task_pending,
            "messages": []  # Default to empty list for unsaved instances
        }

        # Only try to access messages if this instance has been saved to the database
        if self.pk is not None:
            try:
                result["messages"] = [msg.to_dict() for msg in self.messages.all().order_by('timestamp')]
            except Exception as e:
                # If there's an error accessing messages, log it and use empty list
                import logging
                logging.warning(f"Error accessing messages for GameSession {self.pk}: {str(e)}")
                result["messages"] = []

        # Add company information if available
        if self.company:
            result["company"] = {
                "id": self.company.id,
                "name": self.company.name,
                "team": self.team
            }

        return result

    def __str__(self):
        if self.user:
            return f"Game for {self.user.username}"
        return f"Game for session {self.session_id}"

    def generate_certificate(self):
        """
        Generate a certificate for this game session if it's completed.
        Returns the certificate object if successful, None otherwise.
        """
        if not self.game_completed or not self.user:
            return None

        try:
            # Import here to avoid circular imports
            from corporate.certificate_generator import generate_certificate_pdf

            # Generate the certificate
            _, certificate = generate_certificate_pdf(
                user=self.user,
                game_session=self,
                template='standard'
            )

            return certificate
        except Exception as e:
            # Log the error but don't crash
            import logging
            logging.error(f"Error generating certificate: {str(e)}")
            return None

    def update_leaderboard(self):
        """
        Update the leaderboard with this game session's results.
        """
        if not self.user:
            return

        try:
            # Import here to avoid circular imports
            from corporate.models import Leaderboard, LeaderboardEntry, CorporateUser

            # First try to get the company directly from the game session
            company = None
            if self.company:
                company = self.company
                import logging
                logging.info(f"Using company {company.name} from game session for leaderboard update")
            else:
                # If no company in game session, try to get it from the user's corporate profile
                try:
                    corporate_profile = CorporateUser.objects.get(user=self.user)
                    company = corporate_profile.company

                    # Update the game session with this company
                    self.company = company
                    self.save(update_fields=['company'])
                    import logging
                    logging.info(f"Found and set company {company.name} from corporate profile for leaderboard update")
                except Exception as e:
                    import logging
                    logging.error(f"Error getting company from corporate profile: {str(e)}")
                    import traceback
                    logging.error(traceback.format_exc())

            # If we still don't have a company, skip the leaderboard update
            if not company:
                import logging
                logging.warning(f"Game session {self.id} doesn't have a company and couldn't find one from corporate profile, skipping leaderboard update")
                return

            # Always use the standard game_id and game_name for consistency
            game_id = "corporate_prompt_master"  # Standard game ID
            game_name = "Corporate Prompt Master"  # Standard game name

            # We no longer use different game_ids for different courses to ensure consistency
            # This ensures all entries appear in the same leaderboard

            # Log the leaderboard update attempt
            import logging
            logging.info(f"Updating leaderboard for user {self.user.username} with score {self.performance_score} in company {company.name}")

            # Determine game status
            if self.game_completed:
                game_status = "Completed"
            else:
                game_status = "In Progress"

            # Define time periods to update
            time_periods = ['all_time', 'monthly', 'weekly', 'daily']

            # Get current timestamp for recorded_at
            from django.utils import timezone
            now = timezone.now()

            # Update leaderboards for all time periods
            for period in time_periods:
                # Update company leaderboard for this period
                company_leaderboard, created_leaderboard = Leaderboard.objects.get_or_create(
                    company=company,
                    time_period=period,
                    defaults={
                        'name': f"{company.name} Leaderboard ({period.replace('_', ' ').title()})",
                        'is_global': False
                    }
                )

                if created_leaderboard:
                    logging.info(f"Created new company leaderboard for {company.name} with period {period}")

                # Update or create the leaderboard entry
                entry, created = LeaderboardEntry.objects.update_or_create(
                    leaderboard=company_leaderboard,
                    user=self.user,
                    game_id=game_id,
                    defaults={
                        'score': self.performance_score,
                        'highest_role': self.current_role,
                        'game_name': game_name,
                        'game_status': game_status,
                        'recorded_at': now  # Use the same timestamp for all entries
                    }
                )
                logging.info(f"{'Created' if created else 'Updated'} leaderboard entry for {self.user.username} in {company.name} {period} leaderboard")

                # Update global leaderboard if allowed
                if company.allow_leaderboard:
                    global_leaderboard, created_global = Leaderboard.objects.get_or_create(
                        is_global=True,
                        time_period=period,
                        defaults={
                            'name': f"Global Leaderboard ({period.replace('_', ' ').title()})",
                        }
                    )

                    if created_global:
                        logging.info(f"Created new global leaderboard for period {period}")

                    # Update or create the global leaderboard entry
                    global_entry, global_created = LeaderboardEntry.objects.update_or_create(
                        leaderboard=global_leaderboard,
                        user=self.user,
                        game_id=game_id,
                        defaults={
                            'score': self.performance_score,
                            'highest_role': self.current_role,
                            'game_name': game_name,
                            'game_status': game_status,
                            'recorded_at': now  # Use the same timestamp for all entries
                        }
                    )
                    logging.info(f"{'Created' if global_created else 'Updated'} global {period} leaderboard entry for {self.user.username}")

            # We no longer need to update our own CompanyLeaderboard since we're using the corporate Company model
            pass

        except Exception as e:
            # Log the error but don't crash
            import logging
            logging.error(f"Error updating leaderboard: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())

class Message(models.Model):
    """Model to store game messages"""
    game_session = models.ForeignKey(GameSession, related_name='messages', on_delete=models.CASCADE)
    message_id = models.CharField(max_length=100)
    sender = models.CharField(max_length=50)
    text = models.TextField()
    html = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    is_challenge = models.BooleanField(default=False)
    is_markdown = models.BooleanField(default=True)
    # Make is_promotion field optional with null=True
    is_promotion = models.BooleanField(default=False, null=True, blank=True)  # Flag for promotion messages
    task_id = models.CharField(max_length=50, null=True, blank=True)

    def to_dict(self):
        """Convert model to dictionary for API response"""
        result = {
            "id": self.message_id,
            "sender": self.sender,  # Keep the original sender value
            "text": self.text,
            "html": self.html,
            "timestamp": self.timestamp.isoformat(),
            "is_challenge": self.is_challenge,
            "is_markdown": self.is_markdown
        }

        # Add is_promotion if it exists
        try:
            result["is_promotion"] = self.is_promotion
        except AttributeError:
            # If is_promotion doesn't exist, default to False
            result["is_promotion"] = False

        if self.task_id:
            result["task_id"] = self.task_id
        return result

    def __str__(self):
        return f"Message from {self.sender} at {self.timestamp}"


class CompanyGameSettings(models.Model):
    """Model to store company-specific game settings"""
    company = models.OneToOneField(CorporateCompany, on_delete=models.CASCADE, related_name='game_settings')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Game visibility settings
    is_public = models.BooleanField(default=True, help_text="If true, the game is visible to non-company members")
    require_login = models.BooleanField(default=False, help_text="If true, users must be logged in to play")

    # Leaderboard settings
    show_leaderboard = models.BooleanField(default=True, help_text="If true, show company leaderboard")
    show_in_global_leaderboard = models.BooleanField(default=True, help_text="If true, company members appear in global leaderboard")

    # Custom game settings
    custom_welcome_message = models.TextField(blank=True, help_text="Custom welcome message for company members")
    custom_completion_message = models.TextField(blank=True, help_text="Custom completion message for company members")
    help_url = models.URLField(max_length=500, blank=True, null=True, help_text="URL for the help button in the game header")

    # Branding
    use_company_branding = models.BooleanField(default=False, help_text="If true, use company logo and colors in game")
    primary_color = models.CharField(max_length=7, blank=True, help_text="Primary color for game UI (hex code)")

    def __str__(self):
        return f"Game Settings for {self.company.name}"


# We're now using the corporate Leaderboard and LeaderboardEntry models instead of these


class CompanyCourse(models.Model):
    """Model to store company-specific courses or challenge sets"""
    company = models.ForeignKey(CorporateCompany, on_delete=models.CASCADE, related_name='game_courses')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Course status
    is_active = models.BooleanField(default=True)

    # Access control
    is_public = models.BooleanField(default=False, help_text="If true, visible to all company members")
    teams = models.TextField(blank=True, help_text="Comma-separated list of teams with access to this course")

    # Course content
    start_role = models.CharField(max_length=50, default="applicant", help_text="Starting role for this course")
    max_role = models.CharField(max_length=50, blank=True, help_text="Maximum achievable role (leave blank for no limit)")

    def __str__(self):
        return f"{self.name} ({self.company.name})"

    def get_teams_list(self):
        """Get teams as a list"""
        if not self.teams:
            return []
        return [team.strip() for team in self.teams.split(',')]

    def set_teams_list(self, teams_list):
        """Set teams from a list"""
        if not teams_list:
            self.teams = ""
        else:
            self.teams = ",".join(teams_list)


class CourseTask(models.Model):
    """Model to store tasks for company-specific courses"""
    course = models.ForeignKey(CompanyCourse, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=100)
    description = models.TextField()
    role = models.CharField(max_length=50, help_text="Role this task belongs to")
    task_id = models.CharField(max_length=50, unique=True, help_text="Unique identifier for this task")
    order = models.IntegerField(default=0, help_text="Order within the role")

    # Task content
    challenge_text = models.TextField(help_text="The challenge text presented to the user")
    success_criteria = models.TextField(help_text="Criteria for successful completion")

    class Meta:
        ordering = ['role', 'order']

    def __str__(self):
        return f"{self.title} ({self.role})"


class AnonymousPlayerSettings(models.Model):
    """
    Model to store settings for anonymous player data cleanup.
    This is a singleton model - only one instance should exist.
    """
    cleanup_enabled = models.BooleanField(
        default=True,
        help_text="If enabled, anonymous player data will be cleaned up automatically"
    )
    cleanup_hours = models.PositiveIntegerField(
        default=24,
        help_text="Number of hours after which anonymous player data will be deleted"
    )
    last_cleanup = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp of the last cleanup operation"
    )

    def __str__(self):
        return f"Anonymous Player Settings (cleanup after {self.cleanup_hours} hours)"

    class Meta:
        verbose_name = "Anonymous Player Settings"
        verbose_name_plural = "Anonymous Player Settings"


class CourseCompletion(models.Model):
    """Model to track user completion of company courses"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='course_completions')
    course = models.ForeignKey(CompanyCourse, on_delete=models.CASCADE, related_name='completions')
    game_session = models.ForeignKey(GameSession, on_delete=models.SET_NULL, null=True, blank=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Progress tracking
    current_role = models.CharField(max_length=50, default="applicant")
    current_task = models.CharField(max_length=50, blank=True)
    completed_tasks = models.TextField(default="[]", help_text="JSON list of completed task IDs")
    score = models.IntegerField(default=0)

    # Status
    is_completed = models.BooleanField(default=False)

    class Meta:
        unique_together = ('user', 'course')

    def __str__(self):
        status = "Completed" if self.is_completed else "In Progress"
        return f"{self.user.username} - {self.course.name} ({status})"

    def get_completed_tasks(self):
        """Get completed tasks as a list"""
        return json.loads(self.completed_tasks)

    def set_completed_tasks(self, tasks_list):
        """Set completed tasks from a list"""
        self.completed_tasks = json.dumps(tasks_list)

    def add_completed_task(self, task_id):
        """Add a task to the completed tasks list"""
        tasks = self.get_completed_tasks()
        if task_id not in tasks:
            tasks.append(task_id)
            self.set_completed_tasks(tasks)
            return True
        return False
