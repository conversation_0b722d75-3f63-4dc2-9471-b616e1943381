# Generated by Django 5.2 on 2025-05-14 17:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0010_delete_companymembership'),
    ]

    operations = [
        migrations.AddField(
            model_name='corporateuser',
            name='registration_link',
            field=models.ForeignKey(blank=True, help_text='The registration link used to join the company, if any', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='registered_users', to='corporate.registrationlink'),
        ),
        migrations.AddField(
            model_name='corporateuser',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('pending', 'Pending Approval')], default='active', help_text='Whether the user is active or pending admin approval', max_length=20),
        ),
        migrations.AddField(
            model_name='registrationlink',
            name='approval_type',
            field=models.CharField(choices=[('auto', 'Automatic Activation'), ('admin', 'Requires Admin Approval')], default='auto', help_text='Whether users joining via this link need admin approval or are automatically activated.', max_length=20),
        ),
    ]
