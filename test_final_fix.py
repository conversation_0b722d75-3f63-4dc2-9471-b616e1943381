#!/usr/bin/env python
"""
Test the final fix for OpenAI client initialization
"""

try:
    from openai import OpenAI
    import httpx
    
    print("🔧 Testing final fixed OpenAI client initialization...")
    
    # Test the final fixed approach with explicit proxies=None
    try:
        http_client = httpx.Client(
            timeout=httpx.Timeout(30.0),
            follow_redirects=True,
            verify=True,
            # Explicitly set proxies to None to avoid any global proxy settings
            proxies=None
        )
        client = OpenAI(
            api_key="test-key",
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            http_client=http_client
        )
        print("✅ Final fixed OpenAI client initialization successful!")
        
        # Clean up
        http_client.close()
        
    except Exception as e:
        print(f"❌ Final fixed OpenAI client initialization failed: {e}")
        import traceback
        traceback.print_exc()
    
except ImportError as e:
    print(f"❌ Import failed: {e}")

print("\nTest completed")
