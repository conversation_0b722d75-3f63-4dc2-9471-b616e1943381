from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('game', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('allow_leaderboard', models.BooleanField(default=True, help_text='Allow employees to appear on leaderboards')),
            ],
            options={
                'verbose_name_plural': 'Companies',
            },
        ),
        migrations.CreateModel(
            name='CorporateUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_title', models.CharField(blank=True, max_length=255)),
                ('department', models.CharField(blank=True, max_length=255)),
                ('employee_id', models.CharField(blank=True, max_length=50)),
                ('is_company_admin', models.BooleanField(default=False, help_text='Can manage company settings and view all employee data')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='corporate.company')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='corporate_profile', to='auth.user')),
            ],
        ),
        migrations.CreateModel(
            name='Leaderboard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('is_global', models.BooleanField(default=False, help_text='If true, this leaderboard is visible to all companies')),
                ('time_period', models.CharField(choices=[('all_time', 'All Time'), ('monthly', 'Monthly'), ('weekly', 'Weekly'), ('daily', 'Daily')], default='all_time', max_length=20)),
                ('max_entries', models.IntegerField(default=100)),
                ('active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='leaderboards', to='corporate.company')),
            ],
        ),
        migrations.CreateModel(
            name='GameAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('game_id', models.CharField(help_text='Identifier for the game', max_length=100)),
                ('game_name', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('access_granted_at', models.DateTimeField(auto_now_add=True)),
                ('access_expires_at', models.DateTimeField(blank=True, null=True)),
                ('custom_settings', models.JSONField(blank=True, default=dict)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_access', to='corporate.company')),
            ],
            options={
                'verbose_name_plural': 'Game access',
                'unique_together': {('company', 'game_id')},
            },
        ),
        migrations.CreateModel(
            name='LeaderboardEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField()),
                ('highest_role', models.CharField(blank=True, max_length=100)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('leaderboard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='entries', to='corporate.leaderboard')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaderboard_entries', to='auth.user')),
            ],
            options={
                'verbose_name_plural': 'Leaderboard entries',
                'ordering': ['-score'],
            },
        ),
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(default='Prompt Engineering Excellence', max_length=255)),
                ('description', models.TextField(default='Has successfully completed the Corporate Prompt Master training program.')),
                ('issue_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('template', models.CharField(default='standard', max_length=50)),
                ('verification_code', models.CharField(editable=False, max_length=50, unique=True)),
                ('final_score', models.IntegerField(default=0)),
                ('highest_role', models.CharField(blank=True, max_length=100)),
                ('game_session', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='certificate', to='game.gamesession')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='certificates', to='auth.user')),
            ],
        ),
    ]
