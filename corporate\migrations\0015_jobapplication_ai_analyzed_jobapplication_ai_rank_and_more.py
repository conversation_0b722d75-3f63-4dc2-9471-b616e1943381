# Generated by Django 5.2 on 2025-05-15 05:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0014_joblisting_resume_education_certification_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='jobapplication',
            name='ai_analyzed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='ai_rank',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='ai_score',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='joblisting',
            name='ai_requirements',
            field=models.TextField(blank=True, help_text='Specific requirements for AI to analyze in applications (skills, experience, education, etc.)'),
        ),
        migrations.CreateModel(
            name='ApplicationRankingFactor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('factor_name', models.CharField(max_length=100)),
                ('factor_score', models.FloatField(default=0.0)),
                ('factor_weight', models.FloatField(default=1.0)),
                ('factor_details', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ranking_factors', to='corporate.jobapplication')),
            ],
            options={
                'ordering': ['-factor_score'],
            },
        ),
    ]
