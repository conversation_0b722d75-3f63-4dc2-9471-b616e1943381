# Generated manually

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('game', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='owned_game_companies', to='auth.user')),
            ],
            options={
                'verbose_name_plural': 'Companies',
            },
        ),
        migrations.AddField(
            model_name='gamesession',
            name='company',
            field=models.ForeignKey(blank=True, help_text='The company this game session belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='game_sessions', to='game.company'),
        ),
        migrations.AddField(
            model_name='gamesession',
            name='team',
            field=models.CharField(blank=True, help_text='Team or department within the company', max_length=100),
        ),
        migrations.CreateModel(
            name='CompanyGameSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_public', models.BooleanField(default=True, help_text='If true, the game is visible to non-company members')),
                ('require_login', models.BooleanField(default=False, help_text='If true, users must be logged in to play')),
                ('show_leaderboard', models.BooleanField(default=True, help_text='If true, show company leaderboard')),
                ('show_in_global_leaderboard', models.BooleanField(default=True, help_text='If true, company members appear in global leaderboard')),
                ('custom_welcome_message', models.TextField(blank=True, help_text='Custom welcome message for company members')),
                ('custom_completion_message', models.TextField(blank=True, help_text='Custom completion message for company members')),
                ('use_company_branding', models.BooleanField(default=False, help_text='If true, use company logo and colors in game')),
                ('primary_color', models.CharField(blank=True, help_text='Primary color for game UI (hex code)', max_length=7)),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='game_settings', to='game.company')),
            ],
        ),
        migrations.CreateModel(
            name='CompanyLeaderboard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of this leaderboard', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('time_period', models.CharField(choices=[('all_time', 'All Time'), ('monthly', 'Monthly'), ('weekly', 'Weekly'), ('daily', 'Daily')], default='all_time', max_length=20)),
                ('team', models.CharField(blank=True, help_text='If set, this leaderboard is for a specific team', max_length=100)),
                ('is_public', models.BooleanField(default=True, help_text='If true, visible to all company members')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaderboards', to='game.company')),
            ],
        ),
        migrations.CreateModel(
            name='LeaderboardEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField(default=0)),
                ('highest_role', models.CharField(blank=True, max_length=50)),
                ('timestamp', models.DateTimeField(auto_now=True)),
                ('leaderboard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='entries', to='game.companyleaderboard')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auth.user')),
            ],
            options={
                'ordering': ['-score'],
                'unique_together': {('leaderboard', 'user')},
            },
        ),
        migrations.CreateModel(
            name='CompanyCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_public', models.BooleanField(default=False, help_text='If true, visible to all company members')),
                ('teams', models.TextField(blank=True, help_text='Comma-separated list of teams with access to this course')),
                ('start_role', models.CharField(default='applicant', help_text='Starting role for this course', max_length=50)),
                ('max_role', models.CharField(blank=True, help_text='Maximum achievable role (leave blank for no limit)', max_length=50)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='game.company')),
            ],
        ),
        migrations.CreateModel(
            name='CourseTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('role', models.CharField(help_text='Role this task belongs to', max_length=50)),
                ('task_id', models.CharField(help_text='Unique identifier for this task', max_length=50, unique=True)),
                ('order', models.IntegerField(default=0, help_text='Order within the role')),
                ('challenge_text', models.TextField(help_text='The challenge text presented to the user')),
                ('success_criteria', models.TextField(help_text='Criteria for successful completion')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='game.companycourse')),
            ],
            options={
                'ordering': ['role', 'order'],
            },
        ),
        migrations.CreateModel(
            name='CourseCompletion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('current_role', models.CharField(default='applicant', max_length=50)),
                ('current_task', models.CharField(blank=True, max_length=50)),
                ('completed_tasks', models.TextField(default='[]', help_text='JSON list of completed task IDs')),
                ('score', models.IntegerField(default=0)),
                ('is_completed', models.BooleanField(default=False)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='completions', to='game.companycourse')),
                ('game_session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='game.gamesession')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_completions', to='auth.user')),
            ],
            options={
                'unique_together': {('user', 'course')},
            },
        ),
    ]
