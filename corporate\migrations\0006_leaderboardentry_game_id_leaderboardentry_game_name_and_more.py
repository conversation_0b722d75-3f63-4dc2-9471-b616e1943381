# Generated by Django 5.2 on 2025-05-12 13:41

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0005_registrationlink_companyinvitation'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='leaderboardentry',
            name='game_id',
            field=models.CharField(blank=True, help_text='Identifier for the game', max_length=100),
        ),
        migrations.AddField(
            model_name='leaderboardentry',
            name='game_name',
            field=models.CharField(blank=True, help_text='Name of the game', max_length=255),
        ),
        migrations.AddIndex(
            model_name='leaderboardentry',
            index=models.Index(fields=['game_id'], name='corporate_l_game_id_e6c0d7_idx'),
        ),
    ]
