# Corporate Prompt Master Game - Django Edition

This is a Django version of the Corporate Prompt Master game, a role-playing game where you progress through a corporate hierarchy by completing prompt engineering challenges.

## Features

- Role-playing game with multiple corporate roles
- Progress through the corporate hierarchy by completing challenges
- Interactive chat interface with AI managers
- Visual organization chart and role progression
- Dark/light mode support
- Responsive design for mobile, tablet, and desktop

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd prompt_game
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
# On Windows
venv\Scripts\activate
# On macOS/Linux
source venv/bin/activate
```

3. Install the required packages:
```bash
pip install -r requirements.txt
```

4. Apply migrations:
```bash
python manage.py migrate
```

5. Run the development server:
```bash
python manage.py runserver
```

6. Open your browser and navigate to http://127.0.0.1:8000/

## Game Structure

The game is structured around a corporate hierarchy with multiple roles:

1. **Applicant**: The starting role
2. **Junior Assistant**: Entry-level position
3. **Sales Associate**: First promotion
4. **Marketing Associate**: Second promotion
5. And many more roles up to CEO

Each role has specific tasks that must be completed to advance to the next role. Tasks are evaluated based on how well they meet the requirements.

## Technical Details

- Built with Django 5.x
- Uses SQLite for database storage
- JavaScript for frontend interactivity
- Markdown for formatting messages
- OpenAI API for response evaluation (optional)

## Configuration

You can configure the game by setting environment variables in a `.env` file:

```
OPENAI_API_KEY=your_api_key_here
USE_LLM_EVALUATION=True
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
