#!/usr/bin/env python
"""
Test the fixed OpenAI client initialization
"""

try:
    from openai import OpenAI
    import httpx
    
    print("🔧 Testing fixed OpenAI client initialization...")
    
    # Test the fixed approach
    try:
        http_client = httpx.Client(
            timeout=30.0,
            follow_redirects=True
        )
        client = OpenAI(
            api_key="test-key",
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            http_client=http_client
        )
        print("✅ Fixed OpenAI client initialization successful!")
        
        # Clean up
        http_client.close()
        
    except Exception as e:
        print(f"❌ Fixed OpenAI client initialization failed: {e}")
        import traceback
        traceback.print_exc()
    
except ImportError as e:
    print(f"❌ Import failed: {e}")

print("\nTest completed")
