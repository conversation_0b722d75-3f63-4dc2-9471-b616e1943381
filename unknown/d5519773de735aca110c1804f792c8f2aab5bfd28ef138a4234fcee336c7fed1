from django.db import migrations

def create_public_game(apps, schema_editor):
    """
    Create a public game access entry that doesn't require login
    """
    Company = apps.get_model('corporate', 'Company')
    GameAccess = apps.get_model('corporate', 'GameAccess')
    
    # Get the first company (or create one if none exists)
    company = Company.objects.first()
    if not company:
        company = Company.objects.create(
            name="Public Access Company",
            description="This company is used for public game access"
        )
    
    # Check if public game access already exists
    if not GameAccess.objects.filter(game_id='prompt_master', is_public=True).exists():
        # Create public game access
        GameAccess.objects.create(
            company=company,
            game_id='prompt_master',
            game_name='Corporate Prompt Master',
            is_active=True,
            is_public=True
        )

class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0002_gameaccess_is_public'),
    ]

    operations = [
        migrations.RunPython(create_public_game),
    ]
