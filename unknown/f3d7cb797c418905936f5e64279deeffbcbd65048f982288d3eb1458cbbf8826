# Generated by Django 5.2 on 2025-05-13 07:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0007_leaderboardentry_game_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_companies', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='company',
            name='is_active',
            field=models.BooleanField(default=False, help_text='Whether the company is approved and active on the platform'),
        ),
        migrations.AddField(
            model_name='company',
            name='pending_approval',
            field=models.<PERSON><PERSON>anField(default=True, help_text='Whether the company is pending approval by a superadmin'),
        ),
    ]
