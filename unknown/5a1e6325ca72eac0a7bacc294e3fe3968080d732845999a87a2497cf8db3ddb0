# Generated by Django 5.2 on 2025-05-12 13:08

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0004_company_owner'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RegistrationLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('intended_role', models.CharField(choices=[('admin', 'Administrator'), ('member', 'Member')], default='member', help_text='The role the user will be assigned upon joining via this link.', max_length=50)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Optional date/time when this link expires.', null=True)),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Optional limit on the number of times this link can be used.', null=True)),
                ('uses_count', models.PositiveIntegerField(default=0, editable=False)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.CharField(blank=True, help_text="Optional notes for the creator about this link's purpose.", max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='registration_links', to='corporate.company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_corporate_registration_links', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Registration Link',
                'verbose_name_plural': 'Registration Links',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CompanyInvitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('token', models.CharField(max_length=100, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('expired', 'Expired')], default='pending', max_length=20)),
                ('invited_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('accepted_at', models.DateTimeField(blank=True, null=True)),
                ('job_title', models.CharField(blank=True, max_length=255)),
                ('department', models.CharField(blank=True, max_length=255)),
                ('is_admin', models.BooleanField(default=False)),
                ('message', models.TextField(blank=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='corporate.company')),
                ('invited_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_corporate_invitations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Company Invitation',
                'verbose_name_plural': 'Company Invitations',
                'unique_together': {('company', 'email', 'status')},
            },
        ),
    ]
