# Generated by Django 5.0.3 on 2025-04-03 16:40

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_company_is_featured_company_tier'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='company',
            options={'permissions': [('change_company_settings', 'Can change company settings'), ('manage_billing', 'Can manage billing'), ('delete_company_object', 'Can delete company'), ('manage_directory_listing', 'Can manage directory listing'), ('manage_members', 'Can add/remove/change roles of members'), ('manage_invites_links', 'Can manage invitations and registration links'), ('view_company_activity', 'Can view company activity log')], 'verbose_name_plural': 'Companies'},
        ),
        migrations.AlterModelOptions(
            name='membership',
            options={'ordering': ['company__name', 'user__username'], 'permissions': [('change_membership_role', 'Can change the role of a membership'), ('manage_folder_access', 'Can manage folder access for a membership')]},
        ),
    ]
