# Generated by Django 5.2 on 2025-04-07 20:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0015_company_is_active_company_requested_tier_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='featured_expiry_date',
            field=models.DateTimeField(blank=True, help_text='Date and time when the featured status expires (if applicable).', null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='featured_request_pending',
            field=models.BooleanField(db_index=True, default=False, help_text='Indicates if a request to feature this company is pending approval.'),
        ),
        migrations.AddField(
            model_name='company',
            name='requested_featured_duration',
            field=models.CharField(blank=True, choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('annually', 'Annually')], help_text='Duration requested for the featured status.', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='requested_tier_duration',
            field=models.CharField(blank=True, choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('annually', 'Annually')], help_text='Duration requested for the tier upgrade.', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='tier_expiry_date',
            field=models.DateTimeField(blank=True, help_text='Date and time when the current tier expires (if applicable).', null=True),
        ),
    ]
