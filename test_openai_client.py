#!/usr/bin/env python
"""
Test script to debug OpenAI client initialization issues
"""

import os
import sys

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from openai import OpenAI
    print("✅ OpenAI import successful")
    print(f"OpenAI version: {OpenAI.__module__}")
    
    # Test basic client initialization
    print("\n🔧 Testing basic OpenAI client initialization...")
    try:
        client = OpenAI(api_key="test-key")
        print("✅ Basic OpenAI client initialization successful")
    except Exception as e:
        print(f"❌ Basic OpenAI client initialization failed: {e}")
    
    # Test Gemini configuration
    print("\n🔧 Testing Gemini API configuration...")
    try:
        client = OpenAI(
            api_key="AIzaSyD6W5JpEt5tXKkme3ra6ctmyw-inqe97jk",
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )
        print("✅ Gemini API client initialization successful")
    except Exception as e:
        print(f"❌ Gemini API client initialization failed: {e}")
    
    # Test with potential problematic parameters
    print("\n🔧 Testing with various parameters...")
    try:
        client = OpenAI(
            api_key="test-key",
            base_url="https://api.openai.com/v1",
            timeout=30
        )
        print("✅ Client with timeout parameter successful")
    except Exception as e:
        print(f"❌ Client with timeout parameter failed: {e}")
    
    # Check if there are any environment variables that might interfere
    print("\n🔍 Checking environment variables...")
    openai_vars = {k: v for k, v in os.environ.items() if 'OPENAI' in k.upper() or 'PROXY' in k.upper()}
    if openai_vars:
        print("Found OpenAI/Proxy related environment variables:")
        for k, v in openai_vars.items():
            print(f"  {k}: {v}")
    else:
        print("No OpenAI/Proxy related environment variables found")
    
except ImportError as e:
    print(f"❌ OpenAI import failed: {e}")

print("\n" + "="*50)
print("Test completed")
