# Generated by Django 5.0.3 on 2025-03-28 23:30
# Combines operations from previous 0005, 0006, 0007 to fix dependencies

import uuid
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# --- Old Role Seeding Logic Removed ---
# The ROLES dictionary, forwards_seed_roles, and backwards_seed_roles functions
# related to the old Role model were removed as part of cleanup.
# --- End Old Role Seeding Logic ---


class Migration(migrations.Migration):

    # This migration depends on the last valid migration before the problematic ones
    dependencies = [
        ('accounts', '0004_companyinformation_logo'),
        # Removed assistants dependency since the app doesn't exist
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # --- Operations from original 0006 ---
        # Removed migrations.CreateModel(name='Role', ...) operation
        migrations.CreateModel(
            name='Membership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                # Foreign keys added later from 0007
            ],
            options={
                'ordering': ['company__name', 'user__username'],
            },
        ),
        migrations.CreateModel(
            name='RegistrationLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Optional date/time when this link expires.', null=True)),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Optional limit on the number of times this link can be used.', null=True)),
                ('uses_count', models.PositiveIntegerField(default=0, editable=False)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.CharField(blank=True, help_text="Optional notes for the creator about this link's purpose.", max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                 # Foreign keys added later from 0007
            ],
            options={
                'verbose_name': 'Registration Link',
                'verbose_name_plural': 'Registration Links',
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='company',
            name='members',
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='activity_type',
            field=models.CharField(choices=[('user_joined', 'User Joined'), ('invitation_sent', 'Invitation Sent'), ('invitation_accepted', 'Invitation Accepted'), ('invitation_cancelled', 'Invitation Cancelled'), ('invitation_resent', 'Invitation Resent'), ('member_removed', 'Member Removed'), ('role_changed', 'Member Role Changed'), ('folder_access_changed', 'Folder Access Changed'), ('settings_changed', 'Settings Changed'), ('assistant_created', 'Assistant Created'), ('assistant_updated', 'Assistant Updated'), ('assistant_deleted', 'Assistant Deleted'), ('folder_created', 'Folder Created'), ('folder_updated', 'Folder Updated'), ('folder_deleted', 'Folder Deleted')], max_length=50),
        ),
        # --- Operations from original 0007 ---
        # Commented out since assistants app doesn't exist
        # migrations.AddField(
        #     model_name='membership',
        #     name='accessible_folders',
        #     field=models.ManyToManyField(blank=True, related_name='memberships_with_access', to='assistants.assistantfolder'),
        # ),
        migrations.AddField(
            model_name='membership',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='accounts.company'),
        ),
        migrations.AddField(
            model_name='membership',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='registrationlink',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='registration_links', to='accounts.company'),
        ),
        migrations.AddField(
            model_name='registrationlink',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_registration_links', to=settings.AUTH_USER_MODEL),
        ),
        # Removed migrations.AddField operations for 'role' on RegistrationLink, Membership, CompanyInvitation
        migrations.AlterUniqueTogether(
            name='membership',
            unique_together={('user', 'company')},
        ),
        # --- Seed Roles Operation Removed ---
        # Removed migrations.RunPython(forwards_seed_roles, backwards_seed_roles)
    ]
