#!/usr/bin/env python
"""
Test the API call directly
"""

import requests
import urllib.parse

# Test prompt
prompt = """JOB ADVERTISEMENT

Junior Assistant - Rwenzori Innovations Ltd.

About Us: Rwenzori Innovations Ltd. is a leading innovator in the solar solutions industry, creating cutting-edge renewable energy products for businesses throughout Uganda and East Africa. With a team of dedicated professionals, we pride ourselves on quality, innovation, and exceptional customer service.

Role Overview: We are seeking a detail-oriented Junior Assistant to join our dynamic team. The ideal candidate will provide administrative support to various departments while developing professional skills in a fast-paced environment.

Key Responsibilities: - Assist with daily administrative tasks and office operations - Coordinate meetings and manage calendars - Prepare and organize documents and presentations - Respond to emails and phone inquiries - Support team projects and initiatives - Maintain organized filing systems

Requirements: - Excellent communication skills (written and verbal) - Strong attention to detail and organizational abilities - Proficiency in Microsoft Office Suite - Ability to multitask and prioritize effectively - Problem-solving mindset and proactive attitude - Bachelor's degree preferred but not required

Benefits: - Competitive salary - Health and dental insurance - Paid time off - Professional development opportunities - Collaborative work environment

Location: Kampala Business District

Type: Full-time

Please draft a cover letter addressing your qualifications for this position."""

# URL encode the prompt
encoded_prompt = urllib.parse.quote(prompt)

# Make the API call
url = f"https://127.0.0.1:8000/game/api/preview_response/?prompt={encoded_prompt}&task_id=cover_letter"

print("🔧 Testing API call...")
print(f"URL: {url[:100]}...")

try:
    # Disable SSL verification for self-signed certificate
    response = requests.get(url, verify=False, timeout=30)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text[:500]}...")
    
    if response.status_code == 200:
        print("✅ API call successful!")
    else:
        print("❌ API call failed")
        
except Exception as e:
    print(f"❌ Error making API call: {e}")

print("\nTest completed")
