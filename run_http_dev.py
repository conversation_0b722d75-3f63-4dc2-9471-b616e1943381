#!/usr/bin/env python
"""
HTTP-only Development Server Runner

This script runs the Django development server with HTTP support only,
disabling all HTTPS security requirements.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Run the HTTP-only development server."""
    
    # Set Django settings module to HTTP-only version
    os.environ['DJANGO_SETTINGS_MODULE'] = 'prompt_game.settings_http_dev'
    
    # Check if we're in the right directory
    if not Path('manage.py').exists():
        print("Error: manage.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    print("🔓 Starting HTTP-only development server...")
    print("📍 Server will be available at: http://127.0.0.1:8000/")
    print("⚠️  HTTPS security features are disabled for development")
    print("💡 For HTTPS development, use: python run_https_dev.py")
    print()
    
    try:
        # Run the standard Django development server
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped.")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == '__main__':
    main()
