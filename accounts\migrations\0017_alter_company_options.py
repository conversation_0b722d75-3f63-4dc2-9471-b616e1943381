# Generated by Django 5.2 on 2025-04-08 08:59

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0016_company_featured_expiry_date_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='company',
            options={'permissions': [('change_company_settings', 'Can change company settings'), ('manage_billing', 'Can manage billing'), ('delete_company_object', 'Can delete company'), ('manage_directory_listing', 'Can manage directory listing'), ('manage_members', 'Can add/remove/change roles of members'), ('manage_invites_links', 'Can manage invitations and registration links'), ('view_company_activity', 'Can view company activity log'), ('add_assistantfolder', 'Can add assistant folders to the company'), ('change_assistantfolder', 'Can change assistant folders within the company'), ('delete_assistantfolder', 'Can delete assistant folders within the company'), ('view_assistantfolder', 'Can view assistant folders within the company'), ('manage_folder_access', 'Can manage user access to specific folders'), ('change_membership_role', 'Can change member roles'), ('manage_company_assistants', 'Can manage assistants within the company')], 'verbose_name_plural': 'Companies'},
        ),
    ]
