# Generated by Django 5.0.3 on 2025-04-03 14:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0007_companyinformation_custom_domain_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='is_featured',
            field=models.BooleanField(db_index=True, default=False, help_text='Mark this company to appear in the featured section on the directory.'),
        ),
        migrations.AddField(
            model_name='company',
            name='tier',
            field=models.CharField(choices=[('Gold', 'Gold'), ('Silver', 'Silver'), ('Bronze', 'Bronze'), ('Standard', 'Standard')], db_index=True, default='Standard', help_text='Directory display tier for this company.', max_length=10),
        ),
    ]
