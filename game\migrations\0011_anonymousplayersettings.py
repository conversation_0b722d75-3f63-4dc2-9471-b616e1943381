# Generated by Django 5.2 on 2025-05-14 14:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0010_companygamesettings_help_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnonymousPlayerSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cleanup_enabled', models.BooleanField(default=True, help_text='If enabled, anonymous player data will be cleaned up automatically')),
                ('cleanup_hours', models.PositiveIntegerField(default=24, help_text='Number of hours after which anonymous player data will be deleted')),
                ('last_cleanup', models.DateTimeField(blank=True, help_text='Timestamp of the last cleanup operation', null=True)),
            ],
            options={
                'verbose_name': 'Anonymous Player Settings',
                'verbose_name_plural': 'Anonymous Player Settings',
            },
        ),
    ]
