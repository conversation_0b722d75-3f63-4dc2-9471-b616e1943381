#!/usr/bin/env python
"""
Test the new Gemini API key
"""

try:
    from openai import OpenAI
    import httpx
    
    print("🔧 Testing new Gemini API key...")
    
    # Test the new API key
    api_key = "AIzaSyDTjwdVbjGu5Q0v-dP6QsfC4IoKM36g8l8"
    
    try:
        http_client = httpx.Client(
            timeout=httpx.Timeout(30.0),
            follow_redirects=True,
            verify=True,
            proxy=None
        )
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            http_client=http_client
        )
        
        print("✅ Client initialization successful!")
        print("🔄 Making test API call...")
        
        response = client.chat.completions.create(
            model="gemini-2.5-flash-lite-preview-06-17",
            messages=[
                {"role": "user", "content": "Hello, this is a test. Please respond with 'API key is working!'"}
            ],
            max_tokens=50
        )
        
        print("✅ API key works!")
        print(f"Response: {response.choices[0].message.content}")
        
        # Clean up
        http_client.close()
        
    except Exception as e:
        print(f"❌ API key test failed: {e}")
        import traceback
        traceback.print_exc()
    
except ImportError as e:
    print(f"❌ Import failed: {e}")

print("\nTest completed")
