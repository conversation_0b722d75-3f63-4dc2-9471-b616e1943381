# LLM Integration

This document explains the architecture and implementation details of the LLM (Large Language Model) integration in the Corporate Prompt Master game.

## Overview

The Corporate Prompt Master game uses LLMs for three primary purposes:
1. **Response Evaluation**: Analyzing player responses against task requirements
2. **Feedback Generation**: Creating personalized feedback from managers
3. **Response Preview**: Generating example responses for players to review

This integration allows the game to provide sophisticated evaluation and natural-sounding feedback without relying on simple pattern matching or predefined responses.

## LLM Service Providers

The game is designed to work with multiple LLM providers:

### Google Gemini
- Primary provider for evaluation and feedback
- Currently using model: `gemini-2.5-flash-lite-preview-06-17`
- Accessed through an OpenAI-compatible interface
- Configuration in `guideline_evaluation.py` and `llm_feedback_generator.py`

```python
# Example configuration for Gemini API
client = OpenAI(
    api_key=api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)
```

### OpenAI (Alternative)
- Can be used as an alternative provider
- Configuration in `llm_response_generator_openai_format.py`

```python
# Example configuration for OpenAI API
client = OpenAI(api_key=api_key)
```

## Integration Components

### 1. LLM Response Generator (`llm_response_generator_openai_format.py`)

This module handles communication with LLM APIs in a provider-agnostic way:

```python
def generate_response_with_llm(prompt, purpose="general", max_retries=2):
    """
    Generate a response using an LLM API.

    Args:
        prompt (str): The prompt to send to the LLM
        purpose (str): The purpose of the generation (affects temperature and other settings)
        max_retries (int): Maximum number of retry attempts

    Returns:
        str: The generated response
    """
    # Configure API parameters based on purpose
    if purpose == "evaluation":
        temperature = 0.2  # Lower temperature for more consistent evaluations
        max_tokens = 1500
    elif purpose == "feedback":
        temperature = 0.7  # Higher temperature for more varied feedback
        max_tokens = 500
    else:
        temperature = 0.5  # Balanced for general responses
        max_tokens = 1000

    # Call the API with retry logic
    # ...
```

### 2. Enhanced Evaluation (`enhanced_evaluation.py`)

This module uses LLMs to evaluate player responses:

```python
def evaluate_response_with_enhanced_llm(user_response, task_id, task_description):
    """
    Evaluate a user's response using an LLM.

    Args:
        user_response (str): The user's response text
        task_id (str): The ID of the current task
        task_description (str): The description of the task

    Returns:
        dict: Evaluation results
    """
    # Construct evaluation prompt
    evaluation_prompt = f"""
    You are evaluating a response to the following task:

    TASK: {task_description}

    USER RESPONSE:
    {user_response}

    Please evaluate this response on the following criteria:
    1. Completeness (1-10): Does it address all requirements?
    2. Quality (1-10): Is it well-written and structured?
    3. Professionalism (1-10): Is the tone appropriate?
    4. Relevance (1-10): Does it directly address the task?

    Also provide an overall score (1-10) and indicate if it meets minimum requirements.
    Include specific feedback and improvement suggestions.
    """

    # Call the LLM and process results
    # ...
```

### 3. Guideline Evaluation (`guideline_evaluation.py`)

This module evaluates responses against structured guidelines:

```python
def evaluate_response_with_guidelines(user_response, task_id):
    """
    Evaluate a user's response against structured guidelines using an LLM.

    Args:
        user_response (str): The user's response text
        task_id (str): The ID of the current task

    Returns:
        tuple: (grade, score, detailed_feedback, section_scores)
    """
    # Get guidelines for the task
    guidelines = get_guidelines_for_task(task_id)

    # Format guidelines for the prompt
    formatted_guidelines = format_guidelines_for_prompt(guidelines)

    # Construct the evaluation prompt
    prompt = f"""
    You are evaluating a response to a task. The response should follow these guidelines:

    {formatted_guidelines}

    USER RESPONSE:
    {user_response}

    Evaluate how well the response meets each guideline criterion.
    """

    # Call the LLM API and process results
    # ...
```

### 4. Feedback Generator (`llm_feedback_generator.py`)

This module generates personalized feedback from managers:

```python
def generate_manager_feedback(player_response, task_id, manager_name, performance_metrics):
    """
    Generate focused performance feedback from a manager.

    Args:
        player_response (str): The player's response to the task
        task_id (str): The ID of the current task
        manager_name (str): The name of the manager providing feedback
        performance_metrics (dict): Dictionary containing performance metrics

    Returns:
        str: Generated feedback
    """
    # Extract performance data
    grade = performance_metrics.get("grade", "okay")
    overall_score = performance_metrics.get("overall_score", 70)

    # Construct feedback prompt
    feedback_prompt = f"""
    As {manager_name}, provide feedback on this employee's work.

    TASK: [Task description]
    RESPONSE: [Player's response]
    PERFORMANCE: {grade} (Score: {overall_score}/100)

    Your feedback should:
    1. Be in your authentic voice as {manager_name}
    2. Acknowledge specific strengths
    3. Provide constructive criticism
    4. Give 1-2 specific suggestions for improvement
    5. End with clear next steps

    Keep your feedback concise (150-200 words).
    """

    # Call the LLM API and return formatted feedback
    # ...
```

## API Communication Flow

The communication flow between the game and LLM services follows this pattern:

1. **Request Preparation**:
   - Construct a prompt with specific instructions
   - Set appropriate parameters (temperature, max tokens, etc.)
   - Include any necessary context or examples

2. **API Call**:
   - Send the request to the LLM service
   - Handle authentication and rate limiting
   - Implement retry logic for transient failures

3. **Response Processing**:
   - Parse the LLM's response
   - Extract structured data (scores, feedback, etc.)
   - Format the response for display or further processing

4. **Error Handling**:
   - Detect and handle API errors
   - Fall back to alternative methods if needed
   - Log issues for debugging

## Prompt Engineering

The game uses carefully crafted prompts to get consistent, high-quality responses from LLMs:

### Evaluation Prompts

Evaluation prompts include:
- Clear instructions on the evaluation criteria
- The original task description
- The player's response
- A structured format for the expected output

### Feedback Prompts

Feedback prompts include:
- Character information to maintain the manager's voice
- Performance metrics to inform the tone and content
- Specific requirements for the feedback structure
- Guidelines on length and focus

## Error Handling and Fallbacks

The LLM integration includes robust error handling:

1. **Retry Logic**: Automatically retry failed API calls with exponential backoff
2. **Parsing Fallbacks**: Use regex-based extraction when JSON parsing fails
3. **Default Evaluations**: Provide basic evaluations based on response length when LLM calls fail
4. **Timeout Handling**: Set appropriate timeouts to prevent hanging requests

```python
def retry_with_exponential_backoff(func, max_retries=3):
    """
    Retry a function with exponential backoff.

    Args:
        func: The function to retry
        max_retries: Maximum number of retry attempts

    Returns:
        The result of the function call
    """
    retry_count = 0
    while retry_count <= max_retries:
        try:
            return func()
        except Exception as e:
            retry_count += 1
            if retry_count > max_retries:
                raise e

            # Calculate wait time with exponential backoff
            wait_time = (2 ** retry_count) + random.uniform(0, 1)
            logging.warning(f"Retry {retry_count}/{max_retries} after {wait_time:.2f} seconds")
            time.sleep(wait_time)
```

## Performance Considerations

To optimize performance and cost:

1. **Caching**: Cache common responses to reduce API calls
2. **Batching**: Combine multiple evaluation requests when possible
3. **Prompt Optimization**: Keep prompts concise while maintaining clarity
4. **Model Selection**: Use smaller, faster models for simpler tasks
5. **Asynchronous Processing**: Use async calls for non-blocking operations

## Integration with Game Flow

The LLM integration is seamlessly integrated with the game flow:

1. **Task Submission**:
   - Player submits a response
   - Response is sent to the LLM for evaluation
   - Evaluation results determine task completion and score

2. **Feedback Generation**:
   - Based on evaluation results, the LLM generates manager feedback
   - Feedback is personalized to the manager character and performance level
   - Feedback is displayed to the player in the chat interface

3. **Response Preview**:
   - Player can request a preview of their response
   - LLM generates a formatted preview
   - Preview is displayed in a modal dialog

This architecture provides a flexible, robust foundation for integrating LLMs into the game, enabling sophisticated evaluation and natural feedback while handling the challenges of working with external AI services.
