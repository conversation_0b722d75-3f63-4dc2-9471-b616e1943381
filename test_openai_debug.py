#!/usr/bin/env python
"""
Debug script to find the source of the proxies parameter issue
"""

import inspect
import sys

try:
    from openai import OpenAI
    
    # Check the OpenAI constructor signature
    print("🔍 Inspecting OpenAI constructor...")
    sig = inspect.signature(OpenAI.__init__)
    print(f"OpenAI.__init__ signature: {sig}")
    
    # Check if 'proxies' is in the parameters
    params = list(sig.parameters.keys())
    print(f"Parameters: {params}")
    
    if 'proxies' in params:
        print("✅ 'proxies' parameter is expected")
    else:
        print("❌ 'proxies' parameter is NOT expected")
    
    # Try to find where the proxies parameter is coming from
    print("\n🔍 Checking OpenAI module details...")
    print(f"OpenAI module file: {OpenAI.__module__}")
    print(f"OpenAI class file: {inspect.getfile(OpenAI)}")
    
    # Check if there are any decorators or wrappers
    print(f"OpenAI class MRO: {OpenAI.__mro__}")
    
    # Try to create a client with minimal parameters
    print("\n🔧 Testing minimal client creation...")
    try:
        # Try with just api_key
        client = OpenAI(api_key="test")
        print("✅ Minimal client creation successful")
    except Exception as e:
        print(f"❌ Minimal client creation failed: {e}")
        
        # Try to see what's in the error traceback
        import traceback
        print("Full traceback:")
        traceback.print_exc()
    
    # Check if there are any global variables or patches
    print("\n🔍 Checking for global modifications...")
    import openai
    print(f"openai module attributes: {[attr for attr in dir(openai) if not attr.startswith('_')]}")
    
except Exception as e:
    print(f"❌ Error during inspection: {e}")
    import traceback
    traceback.print_exc()
