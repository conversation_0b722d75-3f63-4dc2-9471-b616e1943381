# Generated by Django 5.0.3 on 2025-03-31 18:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0006_combined_rbac_setup'),
    ]

    operations = [
        migrations.AddField(
            model_name='companyinformation',
            name='custom_domain',
            field=models.Char<PERSON>ield(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='companyinformation',
            name='facebook',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='companyinformation',
            name='founded',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinformation',
            name='industry',
            field=models.Char<PERSON>ield(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='companyinformation',
            name='linkedin',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='companyinformation',
            name='size',
            field=models.<PERSON>r<PERSON>ield(blank=True, max_length=50),
        ),
        migrations.Add<PERSON>ield(
            model_name='companyinformation',
            name='twitter',
            field=models.URLField(blank=True),
        ),
    ]
