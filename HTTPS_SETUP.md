# HTTPS Development Server Setup

This guide explains how to run your Django development server with HTTPS support to avoid the SSL-related errors you were experiencing.

## Quick Start

### Option 1: Use the batch file (Windows)
```bash
run_https.bat
```

### Option 2: Use the Python script
```bash
python run_https_dev.py
```

### Option 3: Run manually
```bash
python manage.py runserver_plus --cert-file localhost 127.0.0.1:8000
```

## What Was Changed

1. **Added django-extensions** to `INSTALLED_APPS` in settings.py
2. **Updated CORS settings** to allow both HTTP and HTTPS origins
3. **Modified HTTPS security settings** to be development-friendly
4. **Enabled DEBUG mode** for development
5. **Created helper scripts** to easily run HTTPS development server

## Browser Security Warning

When you first visit `https://127.0.0.1:8000/`, your browser will show a security warning because we're using a self-signed certificate. This is normal for development.

**To proceed:**
1. Click "Advanced" or "Show details"
2. Click "Proceed to 127.0.0.1 (unsafe)" or "Continue to site"
3. The site will load normally

## Why This Was Needed

Your Django settings had HTTPS security features enabled:
- `SECURE_SSL_REDIRECT = True` - Forces HTTPS redirects
- `CSRF_COOKIE_SECURE = True` - Requires HTTPS for CSRF cookies
- `SESSION_COOKIE_SECURE = True` - Requires HTTPS for session cookies

The standard Django development server (`python manage.py runserver`) only supports HTTP, which caused the SSL errors you were seeing.

## Production vs Development

- **Development**: Uses self-signed certificates and relaxed security settings
- **Production**: Should use proper SSL certificates and strict security settings

The settings have been configured to automatically adjust based on the `DEBUG` setting.

## Troubleshooting

If you encounter issues:

1. **Make sure django-extensions is installed:**
   ```bash
   pip install django-extensions
   ```

2. **Try the manual command:**
   ```bash
   python manage.py runserver_plus --cert-file localhost 127.0.0.1:8000
   ```

3. **If you get certificate errors, delete any existing certificates:**
   ```bash
   rm -rf certs/
   ```

4. **For HTTP-only development** (not recommended with current settings):
   ```bash
   python manage.py runserver 127.0.0.1:8000
   ```
   Note: This may cause issues due to the HTTPS security settings.

## Alternative: Disable HTTPS Requirements

If you prefer to use HTTP for development, you can modify `settings.py`:

```python
# Set these to False for HTTP development
CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False
SECURE_SSL_REDIRECT = False
```

However, using HTTPS in development is recommended as it more closely matches production environments.
