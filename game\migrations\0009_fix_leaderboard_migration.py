# Generated manually to fix migration issue

from django.db import migrations


class Migration(migrations.Migration):
    """
    This migration fixes an issue with the previous migration where
    a NewLeaderboardEntry model is referenced but doesn't have a 'leaderboard' field.
    """

    dependencies = [
        ('game', '0008_alter_gamesession_company_and_more'),
    ]

    operations = [
        # This is an empty migration that just ensures the dependency chain is correct
        # The actual fix is that by having this migration, Django will properly recognize
        # that migration 0008 has been applied and won't try to apply it again
    ]
