# Gemini API Key Setup

## Current Issue

The application is trying to use the Gemini API but getting an authentication error:
```
"API Key not found. Please pass a valid API key."
```

## Solution: Get a Valid Gemini API Key

### Step 1: Get Your Gemini API Key

1. **Visit Google AI Studio**: https://aistudio.google.com/app/apikey
2. **Sign in** with your Google account
3. **Create API Key** → Click "Create API Key"
4. **Copy the generated API key** (it will look like: `AIzaSy...`)

### Step 2: Update the API Key in Your Code

You have two options:

#### Option A: Direct Replacement (Quick Fix)

Replace the API key in these files:
- `game/guideline_evaluation.py` (line 33)
- `game/llm_response_generator_openai_format.py` (line ~240)
- `game/llm_feedback_generator.py` (line ~130)

Change:
```python
api_key = "AIzaSyD6W5JpEt5tXKkme3ra6ctmyw-inqe97jk"  # Old key
```

To:
```python
api_key = "YOUR_NEW_API_KEY_HERE"  # Your new key
```

#### Option B: Environment Variable (Recommended)

1. **Create a `.env` file** in your project root:
```bash
GEMINI_API_KEY=your_actual_api_key_here
```

2. **Update the code** to use environment variables:
```python
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('GEMINI_API_KEY', 'fallback_key_here')
```

### Step 3: Test the API Key

After updating the API key, test it with this simple script:

```python
from openai import OpenAI
import httpx

# Test your new API key
api_key = "YOUR_NEW_API_KEY_HERE"

try:
    http_client = httpx.Client(
        timeout=httpx.Timeout(30.0),
        follow_redirects=True,
        verify=True,
        proxy=None
    )
    
    client = OpenAI(
        api_key=api_key,
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
        http_client=http_client
    )
    
    response = client.chat.completions.create(
        model="gemini-2.5-flash-lite-preview-06-17",
        messages=[
            {"role": "user", "content": "Hello, this is a test."}
        ],
        max_tokens=50
    )
    
    print("✅ API key works!")
    print(f"Response: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"❌ API key test failed: {e}")
```

## Important Notes

1. **Keep your API key secure** - Never commit it to version control
2. **Use environment variables** for production
3. **Monitor your API usage** in Google AI Studio
4. **Check API quotas** if you get rate limit errors

## Current Status

- ✅ HTTPS development server working
- ✅ Database issues resolved
- ✅ Gemini model updated to `gemini-2.5-flash-lite-preview-06-17`
- ✅ OpenAI client initialization fixed
- ❌ **Need valid Gemini API key** to complete the setup

Once you update the API key, all functionality should work perfectly!
