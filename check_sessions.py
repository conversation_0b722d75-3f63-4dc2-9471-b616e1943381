#!/usr/bin/env python
import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession

print("=== GameSession Analysis ===")
print(f"Total sessions: {GameSession.objects.count()}")
print()

sessions = GameSession.objects.all().order_by('id')
for s in sessions:
    print(f"ID: {s.id}")
    print(f"  User: {s.user}")
    print(f"  Session ID: {s.session_id}")
    print(f"  Updated: {s.updated_at}")
    print(f"  Current Role: {s.current_role}")
    print()

# Check for duplicates by user
print("=== Checking for duplicates ===")
from django.contrib.auth.models import User

for user in User.objects.all():
    user_sessions = GameSession.objects.filter(user=user)
    if user_sessions.count() > 1:
        print(f"User {user.username} has {user_sessions.count()} sessions:")
        for s in user_sessions:
            print(f"  - Session {s.id} (updated: {s.updated_at})")

# Check for anonymous sessions
anon_sessions = GameSession.objects.filter(user__isnull=True)
print(f"\nAnonymous sessions: {anon_sessions.count()}")
for s in anon_sessions:
    print(f"  - Session {s.id}, session_id: {s.session_id} (updated: {s.updated_at})")
